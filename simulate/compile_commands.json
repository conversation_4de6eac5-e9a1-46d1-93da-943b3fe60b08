[{"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -std=c++17 -o CMakeFiles/lodepng.dir/_deps/lodepng-src/lodepng.cpp.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src/lodepng.cpp", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src/lodepng.cpp"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -DmjUSEPLATFORMSIMD -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17 -o CMakeFiles/platform_ui_adapter.dir/glfw_adapter.cc.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/glfw_adapter.cc", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/glfw_adapter.cc"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -DmjUSEPLATFORMSIMD -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17 -o CMakeFiles/platform_ui_adapter.dir/glfw_dispatch.cc.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/glfw_dispatch.cc", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/glfw_dispatch.cc"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -DmjUSEPLATFORMSIMD -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17 -o CMakeFiles/platform_ui_adapter.dir/platform_ui_adapter.cc.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/platform_ui_adapter.cc", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/platform_ui_adapter.cc"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -DmjUSEPLATFORMSIMD -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17 -o CMakeFiles/libsimulate.dir/simulate.cc.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/simulate.cc", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/simulate.cc"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/c++ -DmjUSEPLATFORMSIMD -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17 -o CMakeFiles/simulate.dir/main.cc.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/main.cc", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/main.cc"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/context.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/context.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/context.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/init.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/init.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/init.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/input.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/input.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/input.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/monitor.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/monitor.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/monitor.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/vulkan.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/vulkan.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/vulkan.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/window.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/window.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/window.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_init.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_init.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_init.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_monitor.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_monitor.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_monitor.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_window.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_window.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_window.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/xkb_unicode.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/xkb_unicode.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/xkb_unicode.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_time.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_time.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_time.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_thread.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_thread.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_thread.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/glx_context.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/glx_context.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/glx_context.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/egl_context.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/egl_context.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/egl_context.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/osmesa_context.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/osmesa_context.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/osmesa_context.c"}, {"directory": "/home/<USER>/Workplace/cpp/mujoco/simulate", "command": "/usr/bin/cc -D_GLFW_USE_CONFIG_H -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -o _deps/glfw3-build/src/CMakeFiles/glfw.dir/linux_joystick.c.o -c /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/linux_joystick.c", "file": "/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/linux_joystick.c"}]