# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: glfw3-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/

#############################################
# Utility command for glfw3-populate

build glfw3-populate: phony CMakeFiles/glfw3-populate CMakeFiles/glfw3-populate-complete glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-done glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild -B/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/glfw3-populate

build CMakeFiles/glfw3-populate | ${cmake_ninja_workdir}CMakeFiles/glfw3-populate: phony CMakeFiles/glfw3-populate-complete


#############################################
# Custom command for CMakeFiles/glfw3-populate-complete

build CMakeFiles/glfw3-populate-complete glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-done | ${cmake_ninja_workdir}CMakeFiles/glfw3-populate-complete ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-done: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/CMakeFiles && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/CMakeFiles/glfw3-populate-complete && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-done
  DESC = Completed 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build
  DESC = No build step for 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure: CUSTOM_COMMAND glfw3-populate-prefix/tmp/glfw3-populate-cfgcmd.txt glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-configure
  DESC = No configure step for 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-gitinfo.txt glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps && /usr/bin/cmake -P /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/tmp/glfw3-populate-gitclone.cmake && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download
  DESC = Performing download step (git clone) for 'glfw3-populate'
  pool = console
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-build
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install
  DESC = No install step for 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/tmp && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-mkdir
  DESC = Creating directories for 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-download
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-patch
  DESC = No patch step for 'glfw3-populate'
  restat = 1


#############################################
# Custom command for glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test

build glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test | ${cmake_ninja_workdir}glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test: CUSTOM_COMMAND glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-install
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild/glfw3-populate-prefix/src/glfw3-populate-stamp/glfw3-populate-test
  DESC = No test step for 'glfw3-populate'
  restat = 1

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-subbuild

build all: phony glfw3-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/ExternalProject-gitupdate.cmake.in /usr/share/cmake-3.22/Modules/ExternalProject.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt glfw3-populate-prefix/tmp/glfw3-populate-cfgcmd.txt.in
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/ExternalProject-gitupdate.cmake.in /usr/share/cmake-3.22/Modules/ExternalProject.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt glfw3-populate-prefix/tmp/glfw3-populate-cfgcmd.txt.in: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
