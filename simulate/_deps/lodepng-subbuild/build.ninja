# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: lodepng-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/

#############################################
# Utility command for lodepng-populate

build lodepng-populate: phony CMakeFiles/lodepng-populate CMakeFiles/lodepng-populate-complete lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-done lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild -B/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/lodepng-populate

build CMakeFiles/lodepng-populate | ${cmake_ninja_workdir}CMakeFiles/lodepng-populate: phony CMakeFiles/lodepng-populate-complete


#############################################
# Custom command for CMakeFiles/lodepng-populate-complete

build CMakeFiles/lodepng-populate-complete lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-done | ${cmake_ninja_workdir}CMakeFiles/lodepng-populate-complete ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-done: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/CMakeFiles && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/CMakeFiles/lodepng-populate-complete && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-done
  DESC = Completed 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build
  DESC = No build step for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure: CUSTOM_COMMAND lodepng-populate-prefix/tmp/lodepng-populate-cfgcmd.txt lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-configure
  DESC = No configure step for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-gitinfo.txt lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps && /usr/bin/cmake -P /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/tmp/lodepng-populate-gitclone.cmake && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download
  DESC = Performing download step (git clone) for 'lodepng-populate'
  pool = console
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-build
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install
  DESC = No install step for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-build && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/tmp && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src && /usr/bin/cmake -E make_directory /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-mkdir
  DESC = Creating directories for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-patch
  DESC = No patch step for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-install
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-build && /usr/bin/cmake -E echo_append && /usr/bin/cmake -E touch /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-test
  DESC = No test step for 'lodepng-populate'
  restat = 1


#############################################
# Custom command for lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update

build lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update | ${cmake_ninja_workdir}lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-update: CUSTOM_COMMAND lodepng-populate-prefix/src/lodepng-populate-stamp/lodepng-populate-download
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src && /usr/bin/cmake -P /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild/lodepng-populate-prefix/tmp/lodepng-populate-gitupdate.cmake
  DESC = Performing update step for 'lodepng-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-subbuild

build all: phony lodepng-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/ExternalProject-gitupdate.cmake.in /usr/share/cmake-3.22/Modules/ExternalProject.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt lodepng-populate-prefix/tmp/lodepng-populate-cfgcmd.txt.in
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/ExternalProject-gitupdate.cmake.in /usr/share/cmake-3.22/Modules/ExternalProject.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/RepositoryInfo.txt.in CMakeCache.txt CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt lodepng-populate-prefix/tmp/lodepng-populate-cfgcmd.txt.in: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
