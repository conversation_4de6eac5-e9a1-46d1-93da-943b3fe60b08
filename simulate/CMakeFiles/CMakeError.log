Performing C SOURCE FILE Test SUPPORTS_LLD failed with the following output:
Change Dir: /home/<USER>/Workplace/cpp/mujoco/simulate/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/ninja cmTC_f9564 && [1/2] Building C object CMakeFiles/cmTC_f9564.dir/src.c.o
[2/2] Linking C executable cmTC_f9564
FAILED: cmTC_f9564 
: && /usr/bin/cc -fuse-ld=lld  CMakeFiles/cmTC_f9564.dir/src.c.o -o cmTC_f9564   && :
collect2: fatal error: cannot find 'ld'
compilation terminated.
ninja: build stopped: subcommand failed.


Source file was:
int main() {}
