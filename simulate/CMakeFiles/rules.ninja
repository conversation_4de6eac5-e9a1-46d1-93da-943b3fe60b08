# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: mujoco_simulate
# Configurations: Release
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__lodepng_Release
  depfile = $DEP_FILE
  deps = gcc
  command = /usr/bin/c++ $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__lodepng_Release
  command = $PRE_LINK && /usr/bin/cmake -E rm -f $TARGET_FILE && "/usr/bin/gcc-ar-11" cr $TARGET_FILE $LINK_FLAGS $in && "/usr/bin/gcc-ranlib-11" $TARGET_FILE && $POST_BUILD
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__platform_ui_adapter_Release
  depfile = $DEP_FILE
  deps = gcc
  command = /usr/bin/c++ $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__libsimulate_Release
  depfile = $DEP_FILE
  deps = gcc
  command = /usr/bin/c++ $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__libsimulate_Release
  command = $PRE_LINK && /usr/bin/cmake -E rm -f $TARGET_FILE && "/usr/bin/gcc-ar-11" cr $TARGET_FILE $LINK_FLAGS $in && "/usr/bin/gcc-ranlib-11" $TARGET_FILE && $POST_BUILD
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__simulate_Release
  depfile = $DEP_FILE
  deps = gcc
  command = /usr/bin/c++ $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__simulate_Release
  command = $PRE_LINK && /usr/bin/c++ $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__glfw_Release
  depfile = $DEP_FILE
  deps = gcc
  command = /usr/bin/cc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__glfw_Release
  command = $PRE_LINK && /usr/bin/cmake -E rm -f $TARGET_FILE && "/usr/bin/gcc-ar-11" cr $TARGET_FILE $LINK_FLAGS $in && "/usr/bin/gcc-ranlib-11" $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate -B/home/<USER>/Workplace/cpp/mujoco/simulate
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /usr/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /usr/bin/ninja -t targets
  description = All primary targets available:

