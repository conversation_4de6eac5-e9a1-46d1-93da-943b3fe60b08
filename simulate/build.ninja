# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: mujoco_simulate
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Workplace/cpp/mujoco/simulate/
# =============================================================================
# Object build statements for STATIC_LIBRARY target lodepng


#############################################
# Order-only phony target for lodepng

build cmake_object_order_depends_target_lodepng: phony || CMakeFiles/lodepng.dir

build CMakeFiles/lodepng.dir/_deps/lodepng-src/lodepng.cpp.o: CXX_COMPILER__lodepng_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src/lodepng.cpp || cmake_object_order_depends_target_lodepng
  DEP_FILE = CMakeFiles/lodepng.dir/_deps/lodepng-src/lodepng.cpp.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src
  OBJECT_DIR = CMakeFiles/lodepng.dir
  OBJECT_FILE_DIR = CMakeFiles/lodepng.dir/_deps/lodepng-src
  TARGET_COMPILE_PDB = CMakeFiles/lodepng.dir/lodepng.pdb
  TARGET_PDB = lib/liblodepng.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target lodepng


#############################################
# Link the static library lib/liblodepng.a

build lib/liblodepng.a: CXX_STATIC_LIBRARY_LINKER__lodepng_Release CMakeFiles/lodepng.dir/_deps/lodepng-src/lodepng.cpp.o
  LANGUAGE_COMPILE_FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects
  OBJECT_DIR = CMakeFiles/lodepng.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/lodepng.dir/lodepng.pdb
  TARGET_FILE = lib/liblodepng.a
  TARGET_PDB = lib/liblodepng.pdb

# =============================================================================
# Object build statements for OBJECT_LIBRARY target platform_ui_adapter


#############################################
# Order-only phony target for platform_ui_adapter

build cmake_object_order_depends_target_platform_ui_adapter: phony || CMakeFiles/platform_ui_adapter.dir

build CMakeFiles/platform_ui_adapter.dir/glfw_adapter.cc.o: CXX_COMPILER__platform_ui_adapter_Release /home/<USER>/Workplace/cpp/mujoco/simulate/glfw_adapter.cc || cmake_object_order_depends_target_platform_ui_adapter
  DEFINES = -DmjUSEPLATFORMSIMD
  DEP_FILE = CMakeFiles/platform_ui_adapter.dir/glfw_adapter.cc.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include
  OBJECT_DIR = CMakeFiles/platform_ui_adapter.dir
  OBJECT_FILE_DIR = CMakeFiles/platform_ui_adapter.dir
  TARGET_COMPILE_PDB = CMakeFiles/platform_ui_adapter.dir/
  TARGET_PDB = ""

build CMakeFiles/platform_ui_adapter.dir/glfw_dispatch.cc.o: CXX_COMPILER__platform_ui_adapter_Release /home/<USER>/Workplace/cpp/mujoco/simulate/glfw_dispatch.cc || cmake_object_order_depends_target_platform_ui_adapter
  DEFINES = -DmjUSEPLATFORMSIMD
  DEP_FILE = CMakeFiles/platform_ui_adapter.dir/glfw_dispatch.cc.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include
  OBJECT_DIR = CMakeFiles/platform_ui_adapter.dir
  OBJECT_FILE_DIR = CMakeFiles/platform_ui_adapter.dir
  TARGET_COMPILE_PDB = CMakeFiles/platform_ui_adapter.dir/
  TARGET_PDB = ""

build CMakeFiles/platform_ui_adapter.dir/platform_ui_adapter.cc.o: CXX_COMPILER__platform_ui_adapter_Release /home/<USER>/Workplace/cpp/mujoco/simulate/platform_ui_adapter.cc || cmake_object_order_depends_target_platform_ui_adapter
  DEFINES = -DmjUSEPLATFORMSIMD
  DEP_FILE = CMakeFiles/platform_ui_adapter.dir/platform_ui_adapter.cc.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include
  OBJECT_DIR = CMakeFiles/platform_ui_adapter.dir
  OBJECT_FILE_DIR = CMakeFiles/platform_ui_adapter.dir
  TARGET_COMPILE_PDB = CMakeFiles/platform_ui_adapter.dir/
  TARGET_PDB = ""



#############################################
# Object library platform_ui_adapter

build platform_ui_adapter: phony CMakeFiles/platform_ui_adapter.dir/glfw_adapter.cc.o CMakeFiles/platform_ui_adapter.dir/glfw_dispatch.cc.o CMakeFiles/platform_ui_adapter.dir/platform_ui_adapter.cc.o

# =============================================================================
# Object build statements for STATIC_LIBRARY target libsimulate


#############################################
# Order-only phony target for libsimulate

build cmake_object_order_depends_target_libsimulate: phony || cmake_object_order_depends_target_lodepng cmake_object_order_depends_target_platform_ui_adapter

build CMakeFiles/libsimulate.dir/simulate.cc.o: CXX_COMPILER__libsimulate_Release /home/<USER>/Workplace/cpp/mujoco/simulate/simulate.cc || cmake_object_order_depends_target_libsimulate
  DEFINES = -DmjUSEPLATFORMSIMD
  DEP_FILE = CMakeFiles/libsimulate.dir/simulate.cc.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include
  OBJECT_DIR = CMakeFiles/libsimulate.dir
  OBJECT_FILE_DIR = CMakeFiles/libsimulate.dir
  TARGET_COMPILE_PDB = CMakeFiles/libsimulate.dir/libsimulate.pdb
  TARGET_PDB = lib/libsimulate.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target libsimulate


#############################################
# Link the static library lib/libsimulate.a

build lib/libsimulate.a: CXX_STATIC_LIBRARY_LINKER__libsimulate_Release CMakeFiles/platform_ui_adapter.dir/glfw_adapter.cc.o CMakeFiles/platform_ui_adapter.dir/glfw_dispatch.cc.o CMakeFiles/platform_ui_adapter.dir/platform_ui_adapter.cc.o CMakeFiles/libsimulate.dir/simulate.cc.o || lib/liblodepng.a platform_ui_adapter /usr/local/lib/libmujoco.so.3.3.4
  LANGUAGE_COMPILE_FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects
  OBJECT_DIR = CMakeFiles/libsimulate.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/libsimulate.dir/libsimulate.pdb
  TARGET_FILE = lib/libsimulate.a
  TARGET_PDB = lib/libsimulate.pdb

# =============================================================================
# Object build statements for EXECUTABLE target simulate


#############################################
# Order-only phony target for simulate

build cmake_object_order_depends_target_simulate: phony || cmake_object_order_depends_target_glfw cmake_object_order_depends_target_libsimulate cmake_object_order_depends_target_lodepng cmake_object_order_depends_target_platform_ui_adapter

build CMakeFiles/simulate.dir/main.cc.o: CXX_COMPILER__simulate_Release /home/<USER>/Workplace/cpp/mujoco/simulate/main.cc || cmake_object_order_depends_target_simulate
  DEFINES = -DmjUSEPLATFORMSIMD
  DEP_FILE = CMakeFiles/simulate.dir/main.cc.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIE -fvisibility=hidden -fvisibility-inlines-hidden -fdata-sections -ffunction-sections -mavx -Werror -Wall -Wpedantic -Wimplicit-fallthrough -Wunused -Wvla -Wno-int-in-bool-context -Wno-sign-compare -Wno-unknown-pragmas -Wimplicit-fallthrough=5 -Wno-maybe-uninitialized -std=c++17
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/lodepng-src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include
  OBJECT_DIR = CMakeFiles/simulate.dir
  OBJECT_FILE_DIR = CMakeFiles/simulate.dir
  TARGET_COMPILE_PDB = CMakeFiles/simulate.dir/
  TARGET_PDB = bin/simulate.pdb


# =============================================================================
# Link build statements for EXECUTABLE target simulate


#############################################
# Link the executable bin/simulate

build bin/simulate: CXX_EXECUTABLE_LINKER__simulate_Release CMakeFiles/simulate.dir/main.cc.o | lib/libsimulate.a lib/libglfw3.a lib/liblodepng.a /usr/local/lib/libmujoco.so.3.3.4 /usr/lib/x86_64-linux-gnu/librt.a /usr/lib/x86_64-linux-gnu/libm.so /usr/lib/x86_64-linux-gnu/libX11.so || lib/libglfw3.a lib/liblodepng.a lib/libsimulate.a platform_ui_adapter
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects
  LINK_FLAGS = -fuse-ld=gold -Wl,--gc-sections
  LINK_LIBRARIES = -Wl,-rpath,/usr/local/lib  lib/libsimulate.a  lib/libglfw3.a  lib/liblodepng.a  /usr/local/lib/libmujoco.so.3.3.4  /usr/lib/x86_64-linux-gnu/librt.a  -lm  -ldl  /usr/lib/x86_64-linux-gnu/libX11.so
  OBJECT_DIR = CMakeFiles/simulate.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/simulate.dir/
  TARGET_FILE = bin/simulate
  TARGET_PDB = bin/simulate.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate -B/home/<USER>/Workplace/cpp/mujoco/simulate
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Workplace/cpp/mujoco/simulate/cmake/SimulateDependencies.cmake
# =============================================================================


#############################################
# Utility command for edit_cache

build _deps/glfw3-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps/glfw3-build/edit_cache: phony _deps/glfw3-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/glfw3-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate -B/home/<USER>/Workplace/cpp/mujoco/simulate
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/glfw3-build/rebuild_cache: phony _deps/glfw3-build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for update_mappings

build _deps/glfw3-build/src/update_mappings: phony _deps/glfw3-build/src/CMakeFiles/update_mappings

# =============================================================================
# Object build statements for STATIC_LIBRARY target glfw


#############################################
# Order-only phony target for glfw

build cmake_object_order_depends_target_glfw: phony || _deps/glfw3-build/src/CMakeFiles/glfw.dir

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/context.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/context.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/context.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/init.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/init.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/init.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/input.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/input.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/input.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/monitor.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/monitor.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/monitor.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/vulkan.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/vulkan.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/vulkan.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/window.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/window.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/window.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_init.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_init.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_init.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_monitor.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_monitor.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_monitor.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_window.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/x11_window.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_window.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/xkb_unicode.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/xkb_unicode.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/xkb_unicode.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_time.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_time.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_time.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_thread.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/posix_thread.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_thread.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/glx_context.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/glx_context.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glx_context.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/egl_context.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/egl_context.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/egl_context.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/osmesa_context.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/osmesa_context.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/osmesa_context.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99 -Wdeclaration-after-statement
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb

build _deps/glfw3-build/src/CMakeFiles/glfw.dir/linux_joystick.c.o: C_COMPILER__glfw_Release /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src/linux_joystick.c || cmake_object_order_depends_target_glfw
  DEFINES = -D_GLFW_USE_CONFIG_H
  DEP_FILE = _deps/glfw3-build/src/CMakeFiles/glfw.dir/linux_joystick.c.o.d
  FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects -fPIC -fvisibility=hidden -fdata-sections -ffunction-sections -Wall -std=c99
  INCLUDES = -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/include -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src -I/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  OBJECT_FILE_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_PDB = lib/libglfw3.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target glfw


#############################################
# Link the static library lib/libglfw3.a

build lib/libglfw3.a: C_STATIC_LIBRARY_LINKER__glfw_Release _deps/glfw3-build/src/CMakeFiles/glfw.dir/context.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/init.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/input.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/monitor.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/vulkan.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/window.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_init.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_monitor.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/x11_window.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/xkb_unicode.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_time.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/posix_thread.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/glx_context.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/egl_context.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/osmesa_context.c.o _deps/glfw3-build/src/CMakeFiles/glfw.dir/linux_joystick.c.o
  LANGUAGE_COMPILE_FLAGS = -O3 -DNDEBUG -flto -fno-fat-lto-objects
  OBJECT_DIR = _deps/glfw3-build/src/CMakeFiles/glfw.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = _deps/glfw3-build/src/CMakeFiles/glfw.dir/glfw.pdb
  TARGET_FILE = lib/libglfw3.a
  TARGET_PDB = lib/libglfw3.pdb


#############################################
# Utility command for edit_cache

build _deps/glfw3-build/src/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps/glfw3-build/src/edit_cache: phony _deps/glfw3-build/src/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/glfw3-build/src/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Workplace/cpp/mujoco/simulate -B/home/<USER>/Workplace/cpp/mujoco/simulate
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/glfw3-build/src/rebuild_cache: phony _deps/glfw3-build/src/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for _deps/glfw3-build/src/CMakeFiles/update_mappings

build _deps/glfw3-build/src/CMakeFiles/update_mappings | ${cmake_ninja_workdir}_deps/glfw3-build/src/CMakeFiles/update_mappings: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/src && /usr/bin/cmake -P /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src/CMake/GenerateMappings.cmake mappings.h.in mappings.h
  DESC = Updating gamepad mappings from upstream repository

# =============================================================================
# Target aliases.

build glfw: phony lib/libglfw3.a

build libglfw3.a: phony lib/libglfw3.a

build liblodepng.a: phony lib/liblodepng.a

build libsimulate: phony lib/libsimulate.a

build libsimulate.a: phony lib/libsimulate.a

build lodepng: phony lib/liblodepng.a

build simulate: phony bin/simulate

build update_mappings: phony _deps/glfw3-build/src/update_mappings

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Workplace/cpp/mujoco/simulate

build all: phony lib/liblodepng.a platform_ui_adapter lib/libsimulate.a bin/simulate

# =============================================================================

#############################################
# Folder: /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build

build _deps/glfw3-build/all: phony _deps/glfw3-build/src/all

# =============================================================================

#############################################
# Folder: /home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build/src

build _deps/glfw3-build/src/all: phony lib/libglfw3.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /usr/local/lib/cmake/mujoco/mujocoConfig.cmake /usr/local/lib/cmake/mujoco/mujocoConfigVersion.cmake /usr/local/lib/cmake/mujoco/mujocoTargets-release.cmake /usr/local/lib/cmake/mujoco/mujocoTargets.cmake /usr/share/cmake-3.22/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.22/Modules/CMakeDependentOption.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.22/Modules/CMakePackageConfigHelpers.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.22/Modules/CheckFunctionExists.c /usr/share/cmake-3.22/Modules/CheckFunctionExists.cmake /usr/share/cmake-3.22/Modules/CheckIncludeFile.c.in /usr/share/cmake-3.22/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.22/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/FetchContent.cmake /usr/share/cmake-3.22/Modules/FetchContent/CMakeLists.cmake.in /usr/share/cmake-3.22/Modules/FindFontconfig.cmake /usr/share/cmake-3.22/Modules/FindFreetype.cmake /usr/share/cmake-3.22/Modules/FindGit.cmake /usr/share/cmake-3.22/Modules/FindOpenGL.cmake /usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.22/Modules/FindPackageMessage.cmake /usr/share/cmake-3.22/Modules/FindPkgConfig.cmake /usr/share/cmake-3.22/Modules/FindThreads.cmake /usr/share/cmake-3.22/Modules/FindX11.cmake /usr/share/cmake-3.22/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/SelectLibraryConfigurations.cmake /usr/share/cmake-3.22/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt _deps/glfw3-src/CMakeLists.txt _deps/glfw3-src/src/CMakeLists.txt _deps/glfw3-src/src/glfw3.pc.in _deps/glfw3-src/src/glfw3Config.cmake.in _deps/glfw3-src/src/glfw_config.h.in cmake/CheckAvxSupport.cmake cmake/FindOrFetch.cmake cmake/MujocoHarden.cmake cmake/MujocoLinkOptions.cmake cmake/SimulateDependencies.cmake cmake/SimulateOptions.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/local/lib/cmake/mujoco/mujocoConfig.cmake /usr/local/lib/cmake/mujoco/mujocoConfigVersion.cmake /usr/local/lib/cmake/mujoco/mujocoTargets-release.cmake /usr/local/lib/cmake/mujoco/mujocoTargets.cmake /usr/share/cmake-3.22/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.22/Modules/CMakeCInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.22/Modules/CMakeDependentOption.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.22/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.22/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.22/Modules/CMakePackageConfigHelpers.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.22/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.22/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.22/Modules/CheckFunctionExists.c /usr/share/cmake-3.22/Modules/CheckFunctionExists.cmake /usr/share/cmake-3.22/Modules/CheckIncludeFile.c.in /usr/share/cmake-3.22/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.22/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.22/Modules/Compiler/GNU.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.22/Modules/FetchContent.cmake /usr/share/cmake-3.22/Modules/FetchContent/CMakeLists.cmake.in /usr/share/cmake-3.22/Modules/FindFontconfig.cmake /usr/share/cmake-3.22/Modules/FindFreetype.cmake /usr/share/cmake-3.22/Modules/FindGit.cmake /usr/share/cmake-3.22/Modules/FindOpenGL.cmake /usr/share/cmake-3.22/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.22/Modules/FindPackageMessage.cmake /usr/share/cmake-3.22/Modules/FindPkgConfig.cmake /usr/share/cmake-3.22/Modules/FindThreads.cmake /usr/share/cmake-3.22/Modules/FindX11.cmake /usr/share/cmake-3.22/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.22/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.22/Modules/Platform/Linux.cmake /usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.22/Modules/SelectLibraryConfigurations.cmake /usr/share/cmake-3.22/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/3.22.1/CMakeCCompiler.cmake CMakeFiles/3.22.1/CMakeCXXCompiler.cmake CMakeFiles/3.22.1/CMakeSystem.cmake CMakeLists.txt _deps/glfw3-src/CMakeLists.txt _deps/glfw3-src/src/CMakeLists.txt _deps/glfw3-src/src/glfw3.pc.in _deps/glfw3-src/src/glfw3Config.cmake.in _deps/glfw3-src/src/glfw_config.h.in cmake/CheckAvxSupport.cmake cmake/FindOrFetch.cmake cmake/MujocoHarden.cmake cmake/MujocoLinkOptions.cmake cmake/SimulateDependencies.cmake cmake/SimulateOptions.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
