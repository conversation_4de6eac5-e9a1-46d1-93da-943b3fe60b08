# This is the CMakeCache file.
# For build in directory: /home/<USER>/Workplace/cpp/mujoco/simulate
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build SHARED libraries
BUILD_SHARED_LIBS:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, recommanded options are: Debug or Release
CMAKE_BUILD_TYPE:STRING=Release

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=MuJoCo simulate binaries

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=https://mujoco.org

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=mujoco_simulate

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=3.3.4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=3

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=3

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=/home/<USER>/Workplace/cpp/mujoco/simulate/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for glfw3
FETCHCONTENT_SOURCE_DIR_GLFW3:PATH=

//When not empty, overrides where to find pre-populated content
// for lodepng
FETCHCONTENT_SOURCE_DIR_LODEPNG:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of glfw3
FETCHCONTENT_UPDATES_DISCONNECTED_GLFW3:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of lodepng
FETCHCONTENT_UPDATES_DISCONNECTED_LODEPNG:BOOL=OFF

//Path to a file.
FREETYPE_INCLUDE_DIR_freetype2:PATH=/usr/include/freetype2

//Path to a file.
FREETYPE_INCLUDE_DIR_ft2build:PATH=/usr/include/freetype2

//Path to a library.
FREETYPE_LIBRARY_DEBUG:FILEPATH=FREETYPE_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FREETYPE_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libfreetype.so

//Path to a file.
Fontconfig_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Fontconfig_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libfontconfig.so

//Git command line client
GIT_EXECUTABLE:FILEPATH=/usr/bin/git

//Value Computed by CMake
GLFW_BINARY_DIR:STATIC=/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-build

//Value Computed by CMake
GLFW_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
GLFW_SOURCE_DIR:STATIC=/home/<USER>/Workplace/cpp/mujoco/simulate/_deps/glfw3-src

//Use OSMesa for offscreen context creation
GLFW_USE_OSMESA:BOOL=OFF

//Use Wayland for window creation
GLFW_USE_WAYLAND:BOOL=OFF

//Assume the Vulkan loader is linked with the application
GLFW_VULKAN_STATIC:BOOL=OFF

//Path to a library.
MATH_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libm.so

//Build libraries as macOS Frameworks
MUJOCO_BUILD_MACOS_FRAMEWORKS:BOOL=OFF

//Version of `glfw` to be fetched.
MUJOCO_DEP_VERSION_glfw3:STRING=7482de6071d21db77a7236155da44c172a7f6c9e

//Version of `lodepng` to be fetched.
MUJOCO_DEP_VERSION_lodepng:STRING=b4ed2cd7ecf61d29076169b49199371456d4f90b

//Build binaries that require AVX instructions, if possible.
MUJOCO_ENABLE_AVX:BOOL=ON

//Make use of hand-written AVX intrinsics, if possible.
MUJOCO_ENABLE_AVX_INTRINSICS:BOOL=ON

//Enable RPath support when installing Mujoco.
MUJOCO_ENABLE_RPATH:BOOL=ON

//Link MuJoCo sample apps and simulate libraries against GLFW statically.
MUJOCO_EXTRAS_STATIC_GLFW:BOOL=ON

//Enable build hardening for MuJoCo.
MUJOCO_HARDEN:BOOL=OFF

//Use installed GLFW version.
MUJOCO_SIMULATE_USE_SYSTEM_GLFW:BOOL=OFF

//Use installed MuJoCo version.
MUJOCO_SIMULATE_USE_SYSTEM_MUJOCO:BOOL=ON

//Path to a file.
OPENGL_EGL_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLX_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENGL_egl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libEGL.so

//Path to a library.
OPENGL_glu_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLU.so

//Path to a library.
OPENGL_glx_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLX.so

//Path to a library.
OPENGL_opengl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenGL.so

//Path to a file.
OPENGL_xmesa_INCLUDE_DIR:PATH=OPENGL_xmesa_INCLUDE_DIR-NOTFOUND

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a library.
RT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/librt.a

//Build the simulate executable binary.
SIMULATE_BUILD_EXECUTABLE:BOOL=ON

//Whether to resolve GLFW symbols dynamically.
SIMULATE_GLFW_DYNAMIC_SYMBOLS:BOOL=OFF

//Path to a file.
X11_ICE_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_ICE_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libICE.so

//Path to a file.
X11_SM_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_SM_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libSM.so

//Path to a file.
X11_X11_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_X11_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libX11.so

//Path to a file.
X11_X11_xcb_INCLUDE_PATH:PATH=X11_X11_xcb_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_X11_xcb_LIB:FILEPATH=X11_X11_xcb_LIB-NOTFOUND

//Path to a file.
X11_XRes_INCLUDE_PATH:PATH=X11_XRes_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_XRes_LIB:FILEPATH=X11_XRes_LIB-NOTFOUND

//Path to a file.
X11_XShm_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_XSync_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xaccessrules_INCLUDE_PATH:PATH=X11_Xaccessrules_INCLUDE_PATH-NOTFOUND

//Path to a file.
X11_Xaccessstr_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xau_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xau_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXau.so

//Path to a file.
X11_Xaw_INCLUDE_PATH:PATH=X11_Xaw_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xaw_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXaw.so

//Path to a file.
X11_Xcomposite_INCLUDE_PATH:PATH=X11_Xcomposite_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xcomposite_LIB:FILEPATH=X11_Xcomposite_LIB-NOTFOUND

//Path to a file.
X11_Xcursor_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xcursor_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXcursor.so

//Path to a file.
X11_Xdamage_INCLUDE_PATH:PATH=X11_Xdamage_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xdamage_LIB:FILEPATH=X11_Xdamage_LIB-NOTFOUND

//Path to a file.
X11_Xdmcp_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xdmcp_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXdmcp.so

//Path to a file.
X11_Xext_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xext_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXext.so

//Path to a file.
X11_Xfixes_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xfixes_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXfixes.so

//Path to a file.
X11_Xft_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xft_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXft.so

//Path to a file.
X11_Xi_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xi_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXi.so

//Path to a file.
X11_Xinerama_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xinerama_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXinerama.so

//Path to a file.
X11_Xkb_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xkblib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xlib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xmu_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xmu_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXmu.so

//Path to a file.
X11_Xpm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xpm_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXpm.so

//Path to a file.
X11_Xrandr_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrandr_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXrandr.so

//Path to a file.
X11_Xrender_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrender_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXrender.so

//Path to a file.
X11_Xshape_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xss_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xss_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXss.so

//Path to a file.
X11_Xt_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xt_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXt.so

//Path to a file.
X11_Xtst_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xtst_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXtst.so

//Path to a file.
X11_Xutil_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xv_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xv_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXv.so

//Path to a file.
X11_Xxf86misc_INCLUDE_PATH:PATH=X11_Xxf86misc_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xxf86misc_LIB:FILEPATH=X11_Xxf86misc_LIB-NOTFOUND

//Path to a file.
X11_Xxf86vm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xxf86vm_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libXxf86vm.so

//Path to a file.
X11_dpms_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_xcb_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libxcb.so

//Path to a file.
X11_xcb_icccm_INCLUDE_PATH:PATH=X11_xcb_icccm_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_icccm_LIB:FILEPATH=X11_xcb_icccm_LIB-NOTFOUND

//Path to a file.
X11_xcb_util_INCLUDE_PATH:PATH=X11_xcb_util_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_util_LIB:FILEPATH=X11_xcb_util_LIB-NOTFOUND

//Path to a file.
X11_xcb_xfixes_INCLUDE_PATH:PATH=X11_xcb_xfixes_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_xfixes_LIB:FILEPATH=X11_xcb_xfixes_LIB-NOTFOUND

//Path to a library.
X11_xcb_xkb_LIB:FILEPATH=X11_xcb_xkb_LIB-NOTFOUND

//Path to a file.
X11_xkbcommon_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xkbcommon_LIB:FILEPATH=/usr/lib/x86_64-linux-gnu/libxkbcommon.so

//Path to a file.
X11_xkbcommon_X11_INCLUDE_PATH:PATH=X11_xkbcommon_X11_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xkbcommon_X11_LIB:FILEPATH=X11_xkbcommon_X11_LIB-NOTFOUND

//Path to a file.
X11_xkbfile_INCLUDE_PATH:PATH=X11_xkbfile_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xkbfile_LIB:FILEPATH=X11_xkbfile_LIB-NOTFOUND

//The directory containing a CMake configuration file for mujoco.
mujoco_DIR:PATH=/usr/local/lib/cmake/mujoco

//Value Computed by CMake
mujoco_simulate_BINARY_DIR:STATIC=/home/<USER>/Workplace/cpp/mujoco/simulate

//Value Computed by CMake
mujoco_simulate_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
mujoco_simulate_SOURCE_DIR:STATIC=/home/<USER>/Workplace/cpp/mujoco/simulate

//Path to a library.
pkgcfg_lib_PKG_FONTCONFIG_fontconfig:FILEPATH=/usr/lib/x86_64-linux-gnu/libfontconfig.so

//Path to a library.
pkgcfg_lib_PKG_FONTCONFIG_freetype:FILEPATH=/usr/lib/x86_64-linux-gnu/libfreetype.so


########################
# INTERNAL cache entries
########################

//Test CAN_BUILD_AVX
CAN_BUILD_AVX:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//STRINGS property for variable: CMAKE_BUILD_TYPE
CMAKE_BUILD_TYPE-STRINGS:INTERNAL=Debug;Release;MinSizeRel;RelWithDebInfo
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Workplace/cpp/mujoco/simulate
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have function connect
CMAKE_HAVE_CONNECT:INTERNAL=1
//Have function gethostbyname
CMAKE_HAVE_GETHOSTBYNAME:INTERNAL=1
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Have function remove
CMAKE_HAVE_REMOVE:INTERNAL=1
//Have function shmat
CMAKE_HAVE_SHMAT:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Workplace/cpp/mujoco/simulate
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//Have library ICE
CMAKE_LIB_ICE_HAS_ICECONNECTIONNUMBER:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=3
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libOpenGL.so][/usr/lib/x86_64-linux-gnu/libGLX.so][/usr/include][c ][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding X11
FIND_PACKAGE_MESSAGE_DETAILS_X11:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libX11.so][c ][v()]
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_freetype2
FREETYPE_INCLUDE_DIR_freetype2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_ft2build
FREETYPE_INCLUDE_DIR_ft2build-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_DEBUG
FREETYPE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_RELEASE
FREETYPE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_INCLUDE_DIR
Fontconfig_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_LIBRARY
Fontconfig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MATH_LIBRARY
MATH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MUJOCO_DEP_VERSION_glfw3
MUJOCO_DEP_VERSION_glfw3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MUJOCO_ENABLE_RPATH
MUJOCO_ENABLE_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_EGL_INCLUDE_DIR
OPENGL_EGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLX_INCLUDE_DIR
OPENGL_GLX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_egl_LIBRARY
OPENGL_egl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glx_LIBRARY
OPENGL_glx_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_opengl_LIBRARY
OPENGL_opengl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_xmesa_INCLUDE_DIR
OPENGL_xmesa_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
PKG_FONTCONFIG_CFLAGS:INTERNAL=-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PKG_FONTCONFIG_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_CFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_FOUND:INTERNAL=1
PKG_FONTCONFIG_INCLUDEDIR:INTERNAL=/usr/include
PKG_FONTCONFIG_INCLUDE_DIRS:INTERNAL=/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PKG_FONTCONFIG_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lfontconfig;-lfreetype
PKG_FONTCONFIG_LDFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_LIBRARIES:INTERNAL=fontconfig;freetype
PKG_FONTCONFIG_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_LIBS:INTERNAL=
PKG_FONTCONFIG_LIBS_L:INTERNAL=
PKG_FONTCONFIG_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_MODULE_NAME:INTERNAL=fontconfig
PKG_FONTCONFIG_PREFIX:INTERNAL=/usr
PKG_FONTCONFIG_STATIC_CFLAGS:INTERNAL=-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PKG_FONTCONFIG_STATIC_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_STATIC_CFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PKG_FONTCONFIG_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lfontconfig;-luuid;-lexpat;-lm;-lfreetype;-lpng16;-lm;-lz;-lm;-lz;-lbrotlidec;-lbrotlicommon
PKG_FONTCONFIG_STATIC_LDFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBDIR:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBRARIES:INTERNAL=fontconfig;uuid;expat;m;freetype;png16;m;z;m;z;brotlidec;brotlicommon
PKG_FONTCONFIG_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_FONTCONFIG_STATIC_LIBS:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_L:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_VERSION:INTERNAL=2.13.1
PKG_FONTCONFIG_fontconfig_INCLUDEDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_LIBDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_PREFIX:INTERNAL=
PKG_FONTCONFIG_fontconfig_VERSION:INTERNAL=
//ADVANCED property for variable: RT_LIBRARY
RT_LIBRARY-ADVANCED:INTERNAL=1
//Test SUPPORTS_GC_SECTIONS
SUPPORTS_GC_SECTIONS:INTERNAL=1
//Test SUPPORTS_GOLD
SUPPORTS_GOLD:INTERNAL=1
//Test SUPPORTS_LLD
SUPPORTS_LLD:INTERNAL=
//ADVANCED property for variable: X11_ICE_INCLUDE_PATH
X11_ICE_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_ICE_LIB
X11_ICE_LIB-ADVANCED:INTERNAL=1
//Have library /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so
X11_LIB_X11_SOLO:INTERNAL=1
//ADVANCED property for variable: X11_SM_INCLUDE_PATH
X11_SM_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_SM_LIB
X11_SM_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_INCLUDE_PATH
X11_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_LIB
X11_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_INCLUDE_PATH
X11_X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_LIB
X11_X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_INCLUDE_PATH
X11_XRes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_LIB
X11_XRes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XShm_INCLUDE_PATH
X11_XShm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XSync_INCLUDE_PATH
X11_XSync_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessrules_INCLUDE_PATH
X11_Xaccessrules_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessstr_INCLUDE_PATH
X11_Xaccessstr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_INCLUDE_PATH
X11_Xau_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_LIB
X11_Xau_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_INCLUDE_PATH
X11_Xaw_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_LIB
X11_Xaw_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_INCLUDE_PATH
X11_Xcomposite_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_LIB
X11_Xcomposite_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_INCLUDE_PATH
X11_Xcursor_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_LIB
X11_Xcursor_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_INCLUDE_PATH
X11_Xdamage_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_LIB
X11_Xdamage_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_INCLUDE_PATH
X11_Xdmcp_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_LIB
X11_Xdmcp_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_INCLUDE_PATH
X11_Xext_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_LIB
X11_Xext_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_INCLUDE_PATH
X11_Xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_LIB
X11_Xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_INCLUDE_PATH
X11_Xft_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_LIB
X11_Xft_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_INCLUDE_PATH
X11_Xi_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_LIB
X11_Xi_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_INCLUDE_PATH
X11_Xinerama_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_LIB
X11_Xinerama_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkb_INCLUDE_PATH
X11_Xkb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkblib_INCLUDE_PATH
X11_Xkblib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xlib_INCLUDE_PATH
X11_Xlib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_INCLUDE_PATH
X11_Xmu_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_LIB
X11_Xmu_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_INCLUDE_PATH
X11_Xpm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_LIB
X11_Xpm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_INCLUDE_PATH
X11_Xrandr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_LIB
X11_Xrandr_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_INCLUDE_PATH
X11_Xrender_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_LIB
X11_Xrender_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xshape_INCLUDE_PATH
X11_Xshape_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_INCLUDE_PATH
X11_Xss_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_LIB
X11_Xss_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_INCLUDE_PATH
X11_Xt_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_LIB
X11_Xt_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_INCLUDE_PATH
X11_Xtst_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_LIB
X11_Xtst_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xutil_INCLUDE_PATH
X11_Xutil_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_INCLUDE_PATH
X11_Xv_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_LIB
X11_Xv_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_INCLUDE_PATH
X11_Xxf86misc_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_LIB
X11_Xxf86misc_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_INCLUDE_PATH
X11_Xxf86vm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_LIB
X11_Xxf86vm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_dpms_INCLUDE_PATH
X11_dpms_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_INCLUDE_PATH
X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_LIB
X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_INCLUDE_PATH
X11_xcb_icccm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_LIB
X11_xcb_icccm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_INCLUDE_PATH
X11_xcb_util_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_LIB
X11_xcb_util_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_INCLUDE_PATH
X11_xcb_xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_LIB
X11_xcb_xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xkb_LIB
X11_xcb_xkb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_INCLUDE_PATH
X11_xkbcommon_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_LIB
X11_xkbcommon_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_INCLUDE_PATH
X11_xkbcommon_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_LIB
X11_xkbcommon_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_INCLUDE_PATH
X11_xkbfile_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_LIB
X11_xkbfile_LIB-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
__pkg_config_arguments_PKG_FONTCONFIG:INTERNAL=QUIET;fontconfig
__pkg_config_checked_PKG_FONTCONFIG:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PKG_FONTCONFIG_fontconfig
pkgcfg_lib_PKG_FONTCONFIG_fontconfig-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PKG_FONTCONFIG_freetype
pkgcfg_lib_PKG_FONTCONFIG_freetype-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

